// Dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

// Initialize dashboard functionality
function initializeDashboard() {
    console.log('Starting dashboard initialization...');

    // Load user information from URL parameters or localStorage
    loadUserInfo();

    // Initialize navigation
    initializeNavigation();

    // Initialize header functionality
    initializeHeaderComponents();

    // Initialize logout functionality
    initializeLogout();

    // Initialize menu bar component (will be initialized separately in DOMContentLoaded)
    console.log('Menu bar component will be initialized separately');

    console.log('Dashboard initialization complete');
}

// Initialize navigation functionality
function initializeNavigation() {
    // Initialize breadcrumb navigation
    initializeBreadcrumbNavigation();

    // Set initial navigation state
    setInitialNavigationState();
}

// Initialize breadcrumb navigation
function initializeBreadcrumbNavigation() {
    // Add click handlers to breadcrumb items for navigation
    const breadcrumbItems = document.querySelectorAll('.breadcrumb span:not(.material-icons)');
    breadcrumbItems.forEach((item, index) => {
        if (index === 0) { // Home breadcrumb
            item.style.cursor = 'pointer';
            item.addEventListener('click', function() {
                navigateToSection('dashboard');
            });
        }
    });
}

// Set initial navigation state
function setInitialNavigationState() {
    // Check URL hash or default to dashboard
    const hash = window.location.hash.substring(1);
    const section = hash || 'dashboard';

    // Update navigation to reflect current section
    updateNavigationState(section);
    updateDashboardSection(section);
}

// Update navigation state - menu bar removed
function updateNavigationState(section) {
    // Menu bar navigation removed - only breadcrumb navigation remains
    console.log(`Navigation state updated to: ${section}`);
}

// Navigate to a specific section
function navigateToSection(section) {
    // Update URL hash
    window.location.hash = section;

    // Update navigation state
    updateNavigationState(section);

    // Update dashboard content
    updateDashboardSection(section);
}

// Load user information
function loadUserInfo() {
    // Get user info from URL parameters or localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const userName = urlParams.get('user') || localStorage.getItem('dashboard_user_name') || 'Administrator';
    const userRole = urlParams.get('role') || localStorage.getItem('dashboard_user_role') || 'Admin';

    // Update UI
    document.getElementById('userName').textContent = userName;

    // Store in localStorage for future use
    localStorage.setItem('dashboard_user_name', userName);
    localStorage.setItem('dashboard_user_role', userRole);
}

// Initialize header components
function initializeHeaderComponents() {
    console.log('Initializing header components...');

    // Initialize integrated search functionality
    initializeIntegratedSearch();
    initializeBranchSelector();
    initializeLanguageSelector();
    initializeReleaseNotes();
    initializeCustomization();
    initializeMonthEnd();
    initializeLogoutButton();
    // Initialize component keyboard shortcuts
    initializeComponentKeyboardShortcuts();

    console.log('Header components initialized');
}

// Old search function removed - using enhanced search component instead

// Initialize enhanced branch selector with search
function initializeBranchSelector() {
    const branchSelector = document.getElementById('branchSelector');
    const branchMenu = document.getElementById('branchMenu');
    const branchSearchInput = document.getElementById('branchSearchInput');
    const branchItemsContainer = document.getElementById('branchItemsContainer');
    const branchNoResults = document.getElementById('branchNoResults');

    if (!branchSelector || !branchMenu) {
        console.warn('Branch selector elements not found');
        return;
    }

    console.log('Branch selector initialized successfully');

    branchSelector.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleDropdown(this, branchMenu);
        // Focus search input when dropdown opens
        setTimeout(() => {
            if (branchSearchInput && branchMenu.classList.contains('show')) {
                branchSearchInput.focus();
            }
        }, 100);
    });

    // Initialize branch search functionality
    if (branchSearchInput) {
        branchSearchInput.addEventListener('input', function() {
            filterBranches(this.value);
        });

        // Prevent dropdown from closing when clicking on search input
        branchSearchInput.addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // Handle keyboard navigation in search
        branchSearchInput.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                const firstVisibleItem = branchItemsContainer.querySelector('.dropdown-item:not(.hidden)');
                if (firstVisibleItem) {
                    firstVisibleItem.focus();
                }
            } else if (e.key === 'Escape') {
                closeDropdown(branchSelector, branchMenu);
            }
        });
    }

    // Handle branch selection
    branchMenu.addEventListener('click', function(e) {
        const branchItem = e.target.closest('.dropdown-item');
        if (branchItem && !branchItem.classList.contains('hidden')) {
            // Remove active class from all items
            this.querySelectorAll('.dropdown-item').forEach(item => {
                item.classList.remove('active');
                // Update check icons
                const checkIcon = item.querySelector('.check-icon');
                if (checkIcon) {
                    checkIcon.textContent = 'radio_button_unchecked';
                }
            });

            // Add active class to selected item
            branchItem.classList.add('active');

            // Update check icon for selected item
            const selectedCheckIcon = branchItem.querySelector('.check-icon');
            if (selectedCheckIcon) {
                selectedCheckIcon.textContent = 'check_circle';
            }

            // Update branch selector button text
            const branchNameElement = branchItem.querySelector('.branch-item-name');
            const currentBranchName = document.getElementById('currentBranchName');
            if (branchNameElement && currentBranchName) {
                currentBranchName.textContent = branchNameElement.textContent;
            }

            // Update tooltip
            const branchText = branchNameElement ? branchNameElement.textContent : branchItem.textContent.trim();
            branchSelector.setAttribute('data-tooltip', `Current Branch: ${branchText}`);

            // Clear search input
            if (branchSearchInput) {
                branchSearchInput.value = '';
                filterBranches(''); // Show all branches
            }

            // Close dropdown
            closeDropdown(branchSelector, branchMenu);

            // Switch branch (placeholder functionality)
            switchBranch(branchItem.dataset.branch);
        }
    });

    // Filter branches function
    function filterBranches(searchTerm) {
        const items = branchItemsContainer.querySelectorAll('.dropdown-item');
        const term = searchTerm.toLowerCase().trim();
        let visibleCount = 0;

        items.forEach(item => {
            const branchName = item.querySelector('.branch-item-name').textContent.toLowerCase();
            const branchCode = item.querySelector('.branch-item-code').textContent.toLowerCase();

            if (branchName.includes(term) || branchCode.includes(term) || term === '') {
                item.classList.remove('hidden');
                visibleCount++;
            } else {
                item.classList.add('hidden');
            }
        });

        // Show/hide no results message
        if (visibleCount === 0 && term !== '') {
            branchNoResults.style.display = 'block';
        } else {
            branchNoResults.style.display = 'none';
        }
    }
}

// Initialize language selector
function initializeLanguageSelector() {
    const languageSelector = document.getElementById('languageSelector');
    const languageMenu = document.getElementById('languageMenu');
    const languageItemsContainer = document.getElementById('languageItemsContainer');

    if (!languageSelector || !languageMenu) {
        console.warn('Language selector elements not found');
        return;
    }

    console.log('Language selector initialized successfully');

    languageSelector.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleDropdown(this, languageMenu);
    });

    // Handle language selection
    languageMenu.addEventListener('click', function(e) {
        const languageItem = e.target.closest('.dropdown-item');
        if (languageItem) {
            // Remove active class from all items
            this.querySelectorAll('.dropdown-item').forEach(item => {
                item.classList.remove('active');
                // Update check icons
                const checkIcon = item.querySelector('.check-icon');
                if (checkIcon) {
                    checkIcon.textContent = 'radio_button_unchecked';
                }
            });

            // Add active class to selected item
            languageItem.classList.add('active');

            // Update check icon for selected item
            const selectedCheckIcon = languageItem.querySelector('.check-icon');
            if (selectedCheckIcon) {
                selectedCheckIcon.textContent = 'check_circle';
            }

            // Update language selector button text
            const languageNameElement = languageItem.querySelector('.language-item-name');
            const currentLanguageName = document.getElementById('currentLanguageName');
            if (languageNameElement && currentLanguageName) {
                currentLanguageName.textContent = languageNameElement.textContent;
            }

            // Update tooltip
            const languageText = languageNameElement ? languageNameElement.textContent : languageItem.textContent.trim();
            languageSelector.setAttribute('data-tooltip', `Current Language: ${languageText}`);

            // Close dropdown
            closeDropdown(languageSelector, languageMenu);

            // Switch language (placeholder functionality)
            switchLanguage(languageItem.dataset.language);
        }
    });
}

// Switch language function (placeholder)
function switchLanguage(languageCode) {
    console.log(`Switching to language: ${languageCode}`);
    // Here you would implement actual language switching logic
    // For example, loading different language files, updating UI text, etc.
}

// Initialize release notes
function initializeReleaseNotes() {
    const releaseNotesButton = document.getElementById('releaseNotesButton');
    const releaseNotesModal = document.getElementById('releaseNotesModal');
    const closeReleaseNotes = document.getElementById('closeReleaseNotes');

    if (!releaseNotesButton || !releaseNotesModal || !closeReleaseNotes) {
        console.warn('Release notes elements not found');
        return;
    }

    releaseNotesButton.addEventListener('click', function() {
        showModal(releaseNotesModal);
        // Mark release notes as read
        const badge = document.getElementById('releaseNotesBadge');
        if (badge) {
            badge.style.display = 'none';
        }
    });

    closeReleaseNotes.addEventListener('click', function() {
        hideModal(releaseNotesModal);
    });

    // Initialize tabs
    initializeReleaseTabs();
}

// Initialize customization
function initializeCustomization() {
    const customizeButton = document.getElementById('customizeButton');
    const customizeModal = document.getElementById('customizeModal');
    const closeCustomize = document.getElementById('closeCustomize');
    const saveCustomization = document.getElementById('saveCustomization');
    const resetCustomization = document.getElementById('resetCustomization');

    if (!customizeButton || !customizeModal || !closeCustomize) {
        console.warn('Customization elements not found');
        return;
    }

    customizeButton.addEventListener('click', function() {
        showModal(customizeModal);
    });

    closeCustomize.addEventListener('click', function() {
        hideModal(customizeModal);
    });

    if (saveCustomization) {
        saveCustomization.addEventListener('click', function() {
            saveCustomizationSettings();
            hideModal(customizeModal);
        });
    }

    if (resetCustomization) {
        resetCustomization.addEventListener('click', function() {
            if (confirm('Are you sure you want to reset all customizations to default?')) {
                resetCustomizationSettings();
            }
        });
    }

    // Initialize customization options
    initializeCustomizationOptions();
}

// Initialize month-end process
function initializeMonthEnd() {
    const monthEndButton = document.getElementById('monthEndButton');
    const monthEndModal = document.getElementById('monthEndModal');
    const closeMonthEnd = document.getElementById('closeMonthEnd');

    if (!monthEndButton || !monthEndModal || !closeMonthEnd) {
        console.warn('Month-end elements not found');
        return;
    }

    monthEndButton.addEventListener('click', function() {
        showModal(monthEndModal);
    });

    closeMonthEnd.addEventListener('click', function() {
        hideModal(monthEndModal);
    });

    // Initialize month-end actions
    initializeMonthEndActions();
}



// Initialize logout button
function initializeLogoutButton() {
    const logoutButton = document.getElementById('logoutButton');

    if (!logoutButton) {
        console.warn('Logout button not found');
        return;
    }

    logoutButton.addEventListener('click', function() {
        handleLogout();
    });
}

// Horizontal navigation removed - menu bar eliminated

















// Update dashboard section based on navigation
function updateDashboardSection(section) {
    const pageTitle = document.querySelector('.page-title');
    const breadcrumb = document.querySelector('.breadcrumb');

    // Update page title and breadcrumb based on section
    switch(section) {
        case 'dashboard':
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
            break;
        case 'core':
            pageTitle.textContent = 'Core Dashboard';
            updateBreadcrumb(['Home', 'Core Dashboard']);
            break;
        case 'helpdesk':
            pageTitle.textContent = 'Helpdesk Dashboard';
            updateBreadcrumb(['Home', 'Helpdesk']);
            break;
        case 'parts':
            pageTitle.textContent = 'Parts Management';
            updateBreadcrumb(['Home', 'Parts']);
            break;
        case 'service':
            pageTitle.textContent = 'Service Management';
            updateBreadcrumb(['Home', 'Service']);
            break;
        case 'tams':
            pageTitle.textContent = 'TAMS Analytics';
            updateBreadcrumb(['Home', 'TAMS']);
            break;
        case 'scheduler':
            pageTitle.textContent = 'Scheduler';
            updateBreadcrumb(['Home', 'Scheduler']);
            break;
        default:
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
    }

    // In a real application, you would load different content here
    console.log(`Navigated to: ${section}`);
}

// Enhanced dropdown utility functions for new header design
function toggleDropdown(trigger, menu) {
    console.log('toggleDropdown called', trigger.id, menu.id);
    const isOpen = trigger.classList.contains('active');

    // Close all other dropdowns
    document.querySelectorAll('.control-button.active, .branch-selector-button.active, .language-selector-button.active').forEach(btn => {
        if (btn !== trigger) {
            btn.classList.remove('active');
            btn.setAttribute('aria-expanded', 'false');
        }
    });

    document.querySelectorAll('.dropdown-menu.show').forEach(dropdown => {
        if (dropdown !== menu) {
            dropdown.classList.remove('show');
        }
    });

    // Toggle current dropdown
    if (isOpen) {
        closeDropdown(trigger, menu);
    } else {
        openDropdown(trigger, menu);
    }
}

function openDropdown(trigger, menu) {
    if (!trigger || !menu) {
        console.warn('Dropdown elements not found');
        return;
    }

    console.log('Opening dropdown', trigger.id, menu.id);
    trigger.classList.add('active');
    trigger.setAttribute('aria-expanded', 'true');
    menu.classList.add('show');

    // Enhanced focus management for new design
    setTimeout(() => {
        const firstFocusable = menu.querySelector('button, a, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            firstFocusable.focus();
        }
    }, 100);
}

function closeDropdown(trigger, menu) {
    if (!trigger || !menu) {
        console.warn('Dropdown elements not found');
        return;
    }

    console.log('Closing dropdown', trigger.id, menu.id);
    trigger.classList.remove('active');
    trigger.setAttribute('aria-expanded', 'false');
    menu.classList.remove('show');

    // Return focus to trigger for better accessibility
    if (trigger && document.activeElement !== trigger) {
        trigger.focus();
    }
}

// Modal utility functions
function showModal(modal) {
    if (!modal) {
        console.warn('Modal element not found');
        return;
    }

    modal.classList.add('show');
    modal.setAttribute('aria-hidden', 'false');
    document.body.style.overflow = 'hidden';

    // Focus management
    const firstFocusable = modal.querySelector('button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
    if (firstFocusable) {
        firstFocusable.focus();
    }
}

function hideModal(modal) {
    if (!modal) {
        console.warn('Modal element not found');
        return;
    }

    modal.classList.remove('show');
    modal.setAttribute('aria-hidden', 'true');
    document.body.style.overflow = '';
}

// Advanced search functionality
function performAdvancedSearch(query, filter) {
    if (query.length < 2) {
        hideSuggestions();
        return;
    }

    console.log(`Searching for: "${query}" in category: ${filter}`);

    // Simulate search results based on filter
    const suggestions = generateSearchSuggestions(query, filter);
    displaySearchSuggestions(suggestions);
}

function generateSearchSuggestions(query, filter) {
    // Mock data for demonstration
    const mockData = {
        all: [
            { icon: 'directions_bus', text: 'Prevost H3-45 Coach', category: 'Vehicle' },
            { icon: 'person', text: 'Metro Transit Authority', category: 'Customer' },
            { icon: 'assessment', text: 'Monthly Sales Report', category: 'Report' },
            { icon: 'business', text: 'North Branch Inventory', category: 'Branch' }
        ],
        vehicles: [
            { icon: 'directions_bus', text: 'Prevost H3-45 Coach', category: 'Vehicle' },
            { icon: 'directions_bus', text: 'Prevost X3-45 Commuter', category: 'Vehicle' },
            { icon: 'directions_bus', text: 'Prevost H5-60 Volvo', category: 'Vehicle' }
        ],
        customers: [
            { icon: 'person', text: 'Metro Transit Authority', category: 'Customer' },
            { icon: 'person', text: 'City Bus Lines', category: 'Customer' },
            { icon: 'person', text: 'Regional Transport Co.', category: 'Customer' }
        ],
        reports: [
            { icon: 'assessment', text: 'Monthly Sales Report', category: 'Report' },
            { icon: 'assessment', text: 'Inventory Analysis', category: 'Report' },
            { icon: 'assessment', text: 'Customer Activity Report', category: 'Report' }
        ]
    };

    const data = mockData[filter] || mockData.all;

    // Filter based on query
    return data.filter(item =>
        item.text.toLowerCase().includes(query.toLowerCase()) ||
        item.category.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 5); // Limit to 5 results
}

function displaySearchSuggestions(suggestions) {
    const searchSuggestions = document.getElementById('searchSuggestions');

    if (suggestions.length === 0) {
        searchSuggestions.innerHTML = `
            <div class="suggestion-item">
                <span class="material-icons suggestion-icon">search_off</span>
                <span class="suggestion-text">No results found</span>
            </div>
        `;
    } else {
        searchSuggestions.innerHTML = suggestions.map(suggestion => `
            <div class="suggestion-item" data-type="${suggestion.category.toLowerCase()}">
                <span class="material-icons suggestion-icon">${suggestion.icon}</span>
                <span class="suggestion-text">${suggestion.text}</span>
                <span class="suggestion-category">${suggestion.category}</span>
            </div>
        `).join('');

        // Add click handlers to suggestions
        searchSuggestions.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', function() {
                const text = this.querySelector('.suggestion-text').textContent;
                const type = this.dataset.type;

                // Update search input
                document.getElementById('globalSearch').value = text;

                // Hide suggestions
                hideSuggestions();

                // Handle selection
                handleSearchSelection(text, type);
            });
        });
    }

    searchSuggestions.classList.add('show');
}

function handleSearchSelection(text, type) {
    console.log(`Selected: ${text} (${type})`);

    // In a real application, this would:
    // 1. Navigate to the appropriate section
    // 2. Filter results based on the selection
    // 3. Update the main content area
}

function hideSuggestions() {
    const searchSuggestions = document.getElementById('searchSuggestions');
    searchSuggestions.classList.remove('show');
}

// Branch switching
function switchBranch(branchId) {
    console.log(`Switching to branch: ${branchId}`);

    // Store selected branch
    localStorage.setItem('selected_branch', branchId);

    // In a real application, this would:
    // 1. Update all data to show branch-specific information
    // 2. Refresh dashboard cards with branch data
    // 3. Update navigation context
}

// Release notes tabs
function initializeReleaseTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // Remove active class from all tabs
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');
            document.getElementById(targetTab + 'Tab').classList.add('active');
        });
    });
}

// Customization options
function initializeCustomizationOptions() {
    // Layout options
    const layoutOptions = document.querySelectorAll('.layout-option');
    layoutOptions.forEach(option => {
        option.addEventListener('click', function() {
            layoutOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Theme options
    const themeOptions = document.querySelectorAll('.theme-option');
    themeOptions.forEach(option => {
        option.addEventListener('click', function() {
            themeOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Load saved customizations
    loadCustomizationSettings();
}

function saveCustomizationSettings() {
    const settings = {
        layout: document.querySelector('.layout-option.active')?.dataset.layout || 'default',
        theme: document.querySelector('.theme-option.active')?.dataset.theme || 'default',
        widgets: {}
    };

    // Get widget visibility settings
    document.querySelectorAll('.widget-toggle input').forEach(input => {
        settings.widgets[input.dataset.widget] = input.checked;
    });

    localStorage.setItem('dashboard_customization', JSON.stringify(settings));

    // Apply settings
    applyCustomizationSettings(settings);
}

function loadCustomizationSettings() {
    const saved = localStorage.getItem('dashboard_customization');
    if (saved) {
        const settings = JSON.parse(saved);
        applyCustomizationSettings(settings);

        // Update UI to reflect saved settings
        document.querySelector(`[data-layout="${settings.layout}"]`)?.classList.add('active');
        document.querySelector(`[data-theme="${settings.theme}"]`)?.classList.add('active');

        Object.entries(settings.widgets || {}).forEach(([widget, visible]) => {
            const toggle = document.querySelector(`[data-widget="${widget}"]`);
            if (toggle) {
                toggle.checked = visible;
            }
        });
    }
}

function resetCustomizationSettings() {
    localStorage.removeItem('dashboard_customization');

    // Reset UI to defaults
    document.querySelectorAll('.layout-option').forEach(opt => opt.classList.remove('active'));
    document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('active'));
    document.querySelector('[data-layout="default"]')?.classList.add('active');
    document.querySelector('[data-theme="default"]')?.classList.add('active');

    document.querySelectorAll('.widget-toggle input').forEach(input => {
        input.checked = true;
    });

    // Apply default settings
    applyCustomizationSettings({
        layout: 'default',
        theme: 'default',
        widgets: {}
    });
}

function applyCustomizationSettings(settings) {
    // Apply layout
    document.body.className = document.body.className.replace(/layout-\w+/g, '');
    document.body.classList.add(`layout-${settings.layout}`);

    // Apply theme
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${settings.theme}`);

    // Apply widget visibility
    Object.entries(settings.widgets || {}).forEach(([widget, visible]) => {
        const element = document.querySelector(`[data-widget-id="${widget}"]`);
        if (element) {
            element.style.display = visible ? '' : 'none';
        }
    });
}

// Month-end process functionality
function initializeMonthEndActions() {
    const actionButtons = document.querySelectorAll('.action-btn');

    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            handleMonthEndAction(action);
        });
    });
}

function handleMonthEndAction(action) {
    switch(action) {
        case 'generate-reports':
            console.log('Generating month-end reports...');
            // Simulate report generation
            setTimeout(() => {
                console.log('Reports generated successfully');
            }, 2000);
            break;

        case 'reconcile-inventory':
            console.log('Starting inventory reconciliation...');
            // Simulate reconciliation process
            setTimeout(() => {
                console.log('Inventory reconciliation completed');
            }, 3000);
            break;

        case 'close-period':
            if (confirm('Are you sure you want to close the current period? This action cannot be undone.')) {
                console.log('Closing period...');
                setTimeout(() => {
                    console.log('Period closed successfully');
                }, 2000);
            }
            break;

        case 'export-data':
            console.log('Preparing data export...');
            // Simulate data export
            setTimeout(() => {
                console.log('Data export ready for download');
                // In a real application, this would trigger a file download
            }, 1500);
            break;
    }
}



function handleLogout() {
    if (confirm('Are you sure you want to logout?')) {
        // Clear stored user data
        localStorage.removeItem('dashboard_user_name');
        localStorage.removeItem('dashboard_user_role');
        localStorage.removeItem('prevost_remembered_credentials');
        localStorage.removeItem('selected_branch');
        localStorage.removeItem('dashboard_customization');

        // Redirect to login page
        window.location.href = 'index.html';
    }
}

// Notification system removed - functionality preserved without popup notifications

// Initialize logout functionality
function initializeLogout() {
    // Logout is now handled through the admin profile dropdown
    // But we keep this function for backward compatibility
}



// Update page content based on navigation
function updatePageContent(section) {
    const pageTitle = document.querySelector('.page-title');
    const breadcrumb = document.querySelector('.breadcrumb');

    // Update page title and breadcrumb based on section
    switch(section) {
        case 'overview':
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
            break;
        case 'inventory':
            pageTitle.textContent = 'Vehicle Inventory';
            updateBreadcrumb(['Home', 'Inventory']);
            break;
        case 'sales':
            pageTitle.textContent = 'Sales Management';
            updateBreadcrumb(['Home', 'Sales']);
            break;
        case 'customers':
            pageTitle.textContent = 'Customer Management';
            updateBreadcrumb(['Home', 'Customers']);
            break;
        case 'branches':
            pageTitle.textContent = 'Branch Management';
            updateBreadcrumb(['Home', 'Management', 'Branches']);
            break;
        case 'users':
            pageTitle.textContent = 'User Management';
            updateBreadcrumb(['Home', 'Management', 'Users']);
            break;
        case 'reports':
            pageTitle.textContent = 'Reports & Analytics';
            updateBreadcrumb(['Home', 'Management', 'Reports']);
            break;
        case 'settings':
            pageTitle.textContent = 'System Settings';
            updateBreadcrumb(['Home', 'Management', 'Settings']);
            break;
        default:
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
    }

    // In a real application, you would load different content here
    console.log(`Navigated to: ${section}`);
}

// Update breadcrumb navigation
function updateBreadcrumb(items) {
    const breadcrumb = document.querySelector('.breadcrumb');
    breadcrumb.innerHTML = '';

    items.forEach((item, index) => {
        const span = document.createElement('span');
        span.textContent = item;
        breadcrumb.appendChild(span);

        if (index < items.length - 1) {
            const icon = document.createElement('span');
            icon.className = 'material-icons';
            icon.textContent = 'chevron_right';
            breadcrumb.appendChild(icon);
        }
    });
}















// Utility function to format numbers
function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Handle window resize for responsive behavior
window.addEventListener('resize', function() {
    // Adjust layout if needed for responsive design
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
        // Mobile-specific adjustments
        document.body.classList.add('mobile-view');
    } else {
        // Desktop-specific adjustments
        document.body.classList.remove('mobile-view');
    }
});

// Initialize responsive behavior on load
window.addEventListener('load', function() {
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
        document.body.classList.add('mobile-view');
    }
});

// Enhanced global event listeners for new header design
document.addEventListener('click', function(event) {
    // Close dropdowns when clicking outside
    if (!event.target.closest('.header-dropdown') &&
        !event.target.closest('.branch-selector-modern') &&
        !event.target.closest('.language-selector') &&
        !event.target.closest('.user-section')) {

        // Close branch selector
        const branchSelector = document.getElementById('branchSelector');
        const branchMenu = document.getElementById('branchMenu');
        if (branchSelector && branchMenu && branchSelector.classList.contains('active')) {
            closeDropdown(branchSelector, branchMenu);
        }

        // Close language selector
        const languageSelector = document.getElementById('languageSelector');
        const languageMenu = document.getElementById('languageMenu');
        if (languageSelector && languageMenu && languageSelector.classList.contains('active')) {
            closeDropdown(languageSelector, languageMenu);
        }
    }

    // Close modals when clicking on overlay
    if (event.target.classList.contains('modal-overlay')) {
        hideModal(event.target);
    }
});

// Add keyboard support for header dropdowns
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        // Close branch selector
        const branchSelector = document.getElementById('branchSelector');
        const branchMenu = document.getElementById('branchMenu');
        if (branchSelector && branchMenu && branchSelector.classList.contains('active')) {
            closeDropdown(branchSelector, branchMenu);
        }

        // Close language selector
        const languageSelector = document.getElementById('languageSelector');
        const languageMenu = document.getElementById('languageMenu');
        if (languageSelector && languageMenu && languageSelector.classList.contains('active')) {
            closeDropdown(languageSelector, languageMenu);
        }
    }
});

// Keyboard navigation support
document.addEventListener('keydown', function(event) {
    // ESC key to close modals and dropdowns
    if (event.key === 'Escape') {
        // Close any open modals
        document.querySelectorAll('.modal-overlay.show').forEach(modal => {
            hideModal(modal);
        });

        // Close any open dropdowns
        document.querySelectorAll('.control-button.active, .branch-selector-button.active').forEach(trigger => {
            const menu = trigger.parentElement.querySelector('.dropdown-menu') ||
                        document.querySelector('.dropdown-menu.show');
            if (menu) {
                closeDropdown(trigger, menu);
            }
        });


    }

    // Ctrl/Cmd + L for logout
    if ((event.ctrlKey || event.metaKey) && event.key === 'l') {
        event.preventDefault();
        handleLogout();
    }

    // Arrow key navigation for dropdowns
    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
        const activeDropdown = document.querySelector('.dropdown-menu.show');
        if (activeDropdown) {
            event.preventDefault();
            navigateDropdown(activeDropdown, event.key === 'ArrowDown');
        }
    }

    // Enter key to select dropdown item
    if (event.key === 'Enter') {
        const focusedItem = document.querySelector('.dropdown-item:focus');
        if (focusedItem) {
            focusedItem.click();
        }
    }
});

function navigateDropdown(dropdown, down) {
    const items = dropdown.querySelectorAll('.dropdown-item');
    const currentFocus = dropdown.querySelector('.dropdown-item:focus');
    let index = currentFocus ? Array.from(items).indexOf(currentFocus) : -1;

    if (down) {
        index = index < items.length - 1 ? index + 1 : 0;
    } else {
        index = index > 0 ? index - 1 : items.length - 1;
    }

    items[index].focus();
}

// Add accessibility enhancements
function enhanceAccessibility() {
    // Horizontal navigation removed - no accessibility enhancements needed

    // Make dropdown items focusable
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    dropdownItems.forEach(item => {
        item.setAttribute('tabindex', '0');
        item.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                this.click();
            }
        });
    });

    // Add ARIA labels to buttons
    const headerButtons = document.querySelectorAll('.header-button');
    headerButtons.forEach(button => {
        if (!button.getAttribute('aria-label')) {
            const text = button.querySelector('.button-text')?.textContent ||
                        button.textContent.trim();
            button.setAttribute('aria-label', text);
        }
    });

    // Add live region for notifications
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    liveRegion.id = 'live-region';
    document.body.appendChild(liveRegion);
}

// Initialize accessibility enhancements
enhanceAccessibility();

/* ========================================
   COMPONENT INTEGRATION
   ======================================== */

// Initialize component integration when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize component integration after main dashboard initialization
    setTimeout(() => {
        initializeComponentIntegration();
    }, 100);
});

// Initialize component integration
function initializeComponentIntegration() {
    console.log('Initializing component integration...');

    // Initialize integrated search functionality
    initializeIntegratedSearch();
    console.log('Integrated search initialized');

    // Initialize menu bar functionality
    initializeMenuBarComponents();
    console.log('Menu bar components initialized');

    // Initialize component keyboard shortcuts
    initializeComponentKeyboardShortcuts();
    console.log('Component keyboard shortcuts initialized');

    console.log('Component integration complete!');
}

// Initialize integrated search functionality
function initializeIntegratedSearch() {
    const searchInput = document.getElementById('search-input');
    const searchResults = document.getElementById('search-results');
    const searchOverlay = document.getElementById('search-overlay');
    const searchResultsClose = document.getElementById('search-results-close');
    const clearRecentBtn = document.getElementById('clear-recent-searches');

    if (!searchInput || !searchResults) return;

    let searchTimeout;
    let selectedIndex = -1;
    let recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');

    // Handle search input
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();

        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (query.length > 0) {
                performSearch(query);
                showSearchResults();
            } else {
                showRecentSearches();
                showSearchResults();
            }
        }, 300);
    });

    // Handle search focus
    searchInput.addEventListener('focus', function() {
        const query = this.value.trim();
        if (query.length > 0) {
            performSearch(query);
        } else {
            showRecentSearches();
        }
        showSearchResults();
    });

    // Handle keyboard navigation
    searchInput.addEventListener('keydown', function(e) {
        const items = searchResults.querySelectorAll('.search-item');

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
            updateSearchSelection(items);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            selectedIndex = Math.max(selectedIndex - 1, -1);
            updateSearchSelection(items);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (selectedIndex >= 0 && items[selectedIndex]) {
                items[selectedIndex].click();
            }
        } else if (e.key === 'Escape') {
            hideSearchResults();
        }
    });

    // Handle overlay click
    if (searchOverlay) {
        searchOverlay.addEventListener('click', hideSearchResults);
    }

    // Handle results close button
    if (searchResultsClose) {
        searchResultsClose.addEventListener('click', hideSearchResults);
    }

    // Handle clear recent searches
    if (clearRecentBtn) {
        clearRecentBtn.addEventListener('click', function() {
            recentSearches = [];
            localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
            showRecentSearches();
        });
    }

    // Handle suggestion tags
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('suggestion-tag')) {
            const suggestion = e.target.dataset.suggestion;
            searchInput.value = suggestion;
            performSearch(suggestion);
        }
    });

    function showSearchResults() {
        searchResults.classList.add('active');
        searchOverlay.classList.add('active');
        selectedIndex = -1;
    }

    function hideSearchResults() {
        searchResults.classList.remove('active');
        searchOverlay.classList.remove('active');
        selectedIndex = -1;
    }

    function updateSearchSelection(items) {
        items.forEach((item, index) => {
            if (index === selectedIndex) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        });
    }

    function performSearch(query) {
        // Initialize search data if not available
        if (!window.searchData) {
            window.searchData = [
                { name: 'Quotation Summary', description: 'View and manage quotations', category: 'Dashboard', icon: 'request_quote' },
                { name: 'Work Order Summary', description: 'Track work orders and progress', category: 'Dashboard', icon: 'build' },
                { name: 'Invoice Summary', description: 'Manage invoices and billing', category: 'Dashboard', icon: 'receipt' },
                { name: 'Warranty Summary', description: 'Warranty claims and coverage', category: 'Dashboard', icon: 'verified_user' },
                { name: 'TAMS Performance', description: 'Technical Asset Management System metrics', category: 'Analytics', icon: 'analytics' },
                { name: 'SAP Interface', description: 'SAP system integration status', category: 'System', icon: 'integration_instructions' },
                { name: 'Branch Management', description: 'Manage branch locations and settings', category: 'Administration', icon: 'business' },
                { name: 'User Profile', description: 'Manage user account and preferences', category: 'Account', icon: 'person' },
                { name: 'Release Notes', description: 'Latest system updates and features', category: 'Information', icon: 'new_releases' },
                { name: 'Dashboard Customization', description: 'Customize dashboard layout and widgets', category: 'Settings', icon: 'tune' },
                { name: 'Month-End Process', description: 'Month-end closing procedures', category: 'Process', icon: 'event_note' },
                { name: 'Parts Management', description: 'Inventory and parts tracking', category: 'Inventory', icon: 'inventory_2' },
                { name: 'Customer Management', description: 'Customer database and relationships', category: 'CRM', icon: 'people' },
                { name: 'Reports', description: 'Generate and view system reports', category: 'Reports', icon: 'assessment' },
                { name: 'Help & Support', description: 'Get help and contact support', category: 'Support', icon: 'help' }
            ];
        }

        const searchData = window.searchData;
        const results = searchData.filter(item =>
            item.name.toLowerCase().includes(query.toLowerCase()) ||
            item.description.toLowerCase().includes(query.toLowerCase()) ||
            item.category.toLowerCase().includes(query.toLowerCase())
        );

        displaySearchResults(results, query);

        // Add to recent searches
        if (query && !recentSearches.includes(query)) {
            recentSearches.unshift(query);
            recentSearches = recentSearches.slice(0, 5); // Keep only 5 recent searches
            localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
        }
    }

    function displaySearchResults(results, query) {
        const searchItems = document.getElementById('search-items');
        const resultsCount = document.querySelector('.results-count');
        const searchTerm = document.querySelector('.search-term');
        const noResults = document.getElementById('search-no-results');
        const searchSection = document.getElementById('search-results-section');
        const recentSection = document.getElementById('recent-searches-section');

        if (!searchItems) return;

        // Update results info
        if (resultsCount) {
            resultsCount.textContent = `${results.length} result${results.length !== 1 ? 's' : ''}`;
        }
        if (searchTerm) {
            searchTerm.textContent = query ? `for "${query}"` : '';
        }

        // Show/hide sections
        if (query) {
            recentSection.style.display = 'none';
            searchSection.style.display = 'block';
        } else {
            recentSection.style.display = 'block';
            searchSection.style.display = 'none';
        }

        // Clear previous results
        searchItems.innerHTML = '';

        if (results.length === 0 && query) {
            if (noResults) {
                noResults.style.display = 'block';
            }
            return;
        }

        if (noResults) {
            noResults.style.display = 'none';
        }

        // Display results
        results.forEach(item => {
            const resultElement = createSearchResultElement(item, query);
            searchItems.appendChild(resultElement);
        });
    }

    function showRecentSearches() {
        const recentSearchesContainer = document.getElementById('recent-searches');
        const recentSection = document.getElementById('recent-searches-section');
        const searchSection = document.getElementById('search-results-section');

        if (!recentSearchesContainer) return;

        // Show recent searches section
        recentSection.style.display = recentSearches.length > 0 ? 'block' : 'none';
        searchSection.style.display = 'none';

        // Clear previous recent searches
        recentSearchesContainer.innerHTML = '';

        // Display recent searches
        recentSearches.forEach(search => {
            const searchElement = document.createElement('button');
            searchElement.className = 'search-item';
            searchElement.innerHTML = `
                <div class="search-item-icon">
                    <span class="material-icons">history</span>
                </div>
                <div class="search-item-content">
                    <div class="search-item-title">${search}</div>
                    <div class="search-item-description">Recent search</div>
                </div>
            `;

            searchElement.addEventListener('click', () => {
                searchInput.value = search;
                performSearch(search);
            });

            recentSearchesContainer.appendChild(searchElement);
        });
    }

    function createSearchResultElement(item, query) {
        const element = document.createElement('button');
        element.className = 'search-item';
        element.innerHTML = `
            <div class="search-item-icon">
                <span class="material-icons">${item.icon || 'description'}</span>
            </div>
            <div class="search-item-content">
                <div class="search-item-title">${highlightText(item.name, query)}</div>
                <div class="search-item-description">${highlightText(item.description, query)}</div>
            </div>
            <div class="search-item-meta">
                <div class="search-item-category">${item.category}</div>
            </div>
        `;

        element.addEventListener('click', () => {
            if (item.href) {
                if (item.href.startsWith('#')) {
                    // Internal navigation
                    const section = item.href.substring(1);
                    navigateToSection(section);
                } else {
                    // External navigation
                    window.location.href = item.href;
                }
            }
            hideSearchResults();
        });

        return element;
    }

    function highlightText(text, query) {
        if (!query) return text;
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    // Initialize recent searches on load
    showRecentSearches();
}

// Search functions removed

// Sidebar components removed completely









// Menu bar components removed - horizontal navigation eliminated





// Initialize component keyboard shortcuts
function initializeComponentKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl+K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Escape to close modals and dropdowns
        if (e.key === 'Escape') {
            // Close all dropdowns
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
            document.querySelectorAll('.control-button.active, .branch-selector-button.active').forEach(btn => {
                btn.classList.remove('active');
                btn.setAttribute('aria-expanded', 'false');
            });

            // Close search results
            const searchResults = document.getElementById('search-results');
            const searchOverlay = document.getElementById('search-overlay');
            if (searchResults && searchResults.classList.contains('active')) {
                searchResults.classList.remove('active');
                searchOverlay.classList.remove('active');
            }
        }
    });
}

// Search results functions removed

// Bookmark functions removed completely

// ===== MENU BAR COMPONENT FUNCTIONALITY =====

/**
 * Menu Bar Component JavaScript
 * Handles dropdown menus, tab switching, and search functionality
 */

class MenuBarComponent {
    constructor() {
        this.activeDropdown = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeTabs();
        this.initializeSearch();
        console.log('Menu Bar Component initialized');
    }

    bindEvents() {
        // Menu button click handlers
        const menuButtons = document.querySelectorAll('.menu-button[data-dropdown]');
        menuButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleDropdown(button);
            });
        });

        // Icon button handlers
        const iconButtons = document.querySelectorAll('.icon-button[data-action]');
        iconButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const action = button.getAttribute('data-action');
                this.handleIconAction(action);
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.menu-item')) {
                this.closeAllDropdowns();
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllDropdowns();
            }
        });
    }

    toggleDropdown(button) {
        const dropdownId = button.getAttribute('data-dropdown');
        const dropdown = document.getElementById(`${dropdownId}-menu`);
        const menuItem = button.closest('.menu-item');

        if (!dropdown) return;

        // Close other dropdowns
        if (this.activeDropdown && this.activeDropdown !== dropdown) {
            this.closeDropdown(this.activeDropdown);
        }

        // Toggle current dropdown
        const isActive = dropdown.classList.contains('active');

        if (isActive) {
            this.closeDropdown(dropdown);
        } else {
            this.openDropdown(dropdown, menuItem);
        }
    }

    openDropdown(dropdown, menuItem) {
        dropdown.classList.add('active');
        menuItem.classList.add('active');
        this.activeDropdown = dropdown;

        // Set ARIA attributes
        const button = menuItem.querySelector('.menu-button');
        button.setAttribute('aria-expanded', 'true');
    }

    closeDropdown(dropdown) {
        if (!dropdown) return;

        dropdown.classList.remove('active');
        const menuItem = dropdown.closest('.menu-item') ||
                        document.querySelector(`[data-dropdown="${dropdown.id.replace('-menu', '')}"]`)?.closest('.menu-item');

        if (menuItem) {
            menuItem.classList.remove('active');
            const button = menuItem.querySelector('.menu-button');
            button.setAttribute('aria-expanded', 'false');
        }

        if (this.activeDropdown === dropdown) {
            this.activeDropdown = null;
        }
    }

    closeAllDropdowns() {
        const activeDropdowns = document.querySelectorAll('.dropdown-menu.active');
        activeDropdowns.forEach(dropdown => {
            this.closeDropdown(dropdown);
        });
    }

    initializeTabs() {
        const tabButtons = document.querySelectorAll('.mega-menu-tab');
        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchTab(button);
            });
        });
    }

    switchTab(activeButton) {
        const tabId = activeButton.getAttribute('data-tab');
        const megaMenu = activeButton.closest('.dropdown-menu');

        if (!megaMenu) return;

        // Remove active class from all tabs in this menu
        const allTabs = megaMenu.querySelectorAll('.mega-menu-tab');
        const allTabContents = megaMenu.querySelectorAll('.mega-menu-tab-content');

        allTabs.forEach(tab => tab.classList.remove('active'));
        allTabContents.forEach(content => content.classList.remove('active'));

        // Add active class to clicked tab
        activeButton.classList.add('active');

        // Show corresponding content
        const targetContent = megaMenu.querySelector(`#${tabId}-content`);
        if (targetContent) {
            targetContent.classList.add('active');
        }
    }

    initializeSearch() {
        const searchInputs = document.querySelectorAll('.mega-menu-search-input');
        searchInputs.forEach(input => {
            const clearButton = input.parentElement.querySelector('.mega-menu-search-clear');

            input.addEventListener('input', (e) => {
                this.handleSearch(e.target, clearButton);
            });

            if (clearButton) {
                clearButton.addEventListener('click', () => {
                    this.clearSearch(input, clearButton);
                });
            }
        });
    }

    handleSearch(input, clearButton) {
        const searchTerm = input.value.toLowerCase().trim();
        const megaMenu = input.closest('.dropdown-menu');

        if (!megaMenu) return;

        // Show/hide clear button
        if (clearButton) {
            clearButton.style.display = searchTerm ? 'block' : 'none';
        }

        // Filter menu items
        const cards = megaMenu.querySelectorAll('.mega-card');
        let hasVisibleItems = false;

        cards.forEach(card => {
            const title = card.querySelector('h4')?.textContent.toLowerCase() || '';
            const description = card.querySelector('p')?.textContent.toLowerCase() || '';
            const isMatch = title.includes(searchTerm) || description.includes(searchTerm);

            card.style.display = isMatch ? 'flex' : 'none';
            if (isMatch) hasVisibleItems = true;
        });

        // Show "No results" message if needed
        this.toggleNoResultsMessage(megaMenu, !hasVisibleItems && searchTerm);
    }

    clearSearch(input, clearButton) {
        input.value = '';
        if (clearButton) {
            clearButton.style.display = 'none';
        }
        this.handleSearch(input, clearButton);
        input.focus();
    }

    toggleNoResultsMessage(megaMenu, show) {
        let noResultsMsg = megaMenu.querySelector('.no-results-message');

        if (show && !noResultsMsg) {
            noResultsMsg = document.createElement('div');
            noResultsMsg.className = 'no-results-message';
            noResultsMsg.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                    <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <p>No items found matching your search.</p>
                </div>
            `;
            const content = megaMenu.querySelector('.mega-menu-content');
            if (content) content.appendChild(noResultsMsg);
        } else if (!show && noResultsMsg) {
            noResultsMsg.remove();
        }
    }

    handleIconAction(action) {
        console.log(`Icon action triggered: ${action}`);

        // Dispatch custom event for external handling
        const event = new CustomEvent('menuIconAction', {
            detail: { action }
        });
        document.dispatchEvent(event);

        // Default actions (can be customized)
        switch (action) {
            case 'dashboard-settings':
                // Trigger existing customization modal
                const customizeModal = document.getElementById('customizeModal');
                if (customizeModal) {
                    showModal(customizeModal);
                } else {
                    this.showMessage('Dashboard Settings clicked');
                }
                break;
            case 'release-notes':
                // Trigger existing release notes modal
                const releaseNotesModal = document.getElementById('releaseNotesModal');
                if (releaseNotesModal) {
                    showModal(releaseNotesModal);
                    // Mark release notes as read
                    const badge = document.getElementById('releaseNotesBadgeMenu');
                    if (badge) {
                        badge.style.display = 'none';
                    }
                } else {
                    this.showMessage('Release Notes clicked');
                }
                break;
            case 'month-end-process':
                // Trigger existing month-end modal
                const monthEndModal = document.getElementById('monthEndModal');
                if (monthEndModal) {
                    showModal(monthEndModal);
                } else {
                    this.showMessage('Month End Process clicked');
                }
                break;
            case 'user-manual':
                this.showMessage('User Manual clicked - Opening user manual...');
                // Here you could open a PDF, navigate to help page, etc.
                // window.open('user-manual.pdf', '_blank');
                break;
            default:
                console.log(`Unknown action: ${action}`);
        }
    }

    showMessage(message) {
        // Simple notification system (can be replaced with your preferred notification library)
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--accent-color);
            color: var(--menu-text);
            padding: 1rem 1.5rem;
            border-radius: 6px;
            box-shadow: 0 4px 12px var(--shadow-medium);
            z-index: 10000;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Public API methods
    openMenu(menuId) {
        const button = document.querySelector(`[data-dropdown="${menuId}"]`);
        if (button) {
            this.toggleDropdown(button);
        }
    }

    closeMenu(menuId) {
        const dropdown = document.getElementById(`${menuId}-menu`);
        if (dropdown) {
            this.closeDropdown(dropdown);
        }
    }

    switchToTab(menuId, tabId) {
        const dropdown = document.getElementById(`${menuId}-menu`);
        if (dropdown) {
            const tabButton = dropdown.querySelector(`[data-tab="${tabId}"]`);
            if (tabButton) {
                this.switchTab(tabButton);
            }
        }
    }
}

// Initialize the menu bar component when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize menu bar component after a short delay to ensure all elements are ready
    setTimeout(() => {
        window.menuBarComponent = new MenuBarComponent();
    }, 100);
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MenuBarComponent;
}
