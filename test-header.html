<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Header Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Dashboard Header Functionality Test</h1>
        <p>This page tests the header functionality of the dashboard. Open the browser console to see detailed logs.</p>
        
        <div class="test-section">
            <h3>🔍 Search Functionality Test</h3>
            <button class="test-button" onclick="testSearch()">Test Search Input</button>
            <button class="test-button" onclick="testSearchKeyboard()">Test Ctrl+K Shortcut</button>
            <div id="search-status" class="status info">Click buttons to test search functionality</div>
        </div>
        
        <div class="test-section">
            <h3>🏢 Branch Selector Test</h3>
            <button class="test-button" onclick="testBranchDropdown()">Test Branch Dropdown</button>
            <button class="test-button" onclick="testBranchSelection()">Test Branch Selection</button>
            <div id="branch-status" class="status info">Click buttons to test branch selector</div>
        </div>
        
        <div class="test-section">
            <h3>🌐 Language Selector Test</h3>
            <button class="test-button" onclick="testLanguageDropdown()">Test Language Dropdown</button>
            <button class="test-button" onclick="testLanguageSelection()">Test Language Selection</button>
            <div id="language-status" class="status info">Click buttons to test language selector</div>
        </div>
        
        <div class="test-section">
            <h3>🎯 Button Functionality Test</h3>
            <button class="test-button" onclick="testHeaderButtons()">Test All Header Buttons</button>
            <button class="test-button" onclick="testMenuBarButtons()">Test Menu Bar Buttons</button>
            <div id="buttons-status" class="status info">Click buttons to test button functionality</div>
        </div>
        
        <div class="test-section">
            <h3>⌨️ Keyboard Shortcuts Test</h3>
            <button class="test-button" onclick="testKeyboardShortcuts()">Test All Shortcuts</button>
            <div id="keyboard-status" class="status info">
                <strong>Available shortcuts:</strong><br>
                • Ctrl+K: Focus search<br>
                • Alt+B: Open branch selector<br>
                • Alt+L: Open language selector<br>
                • Escape: Close dropdowns
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="overall-status" class="status info">Run tests to see overall results</div>
        </div>
    </div>

    <script>
        // Test functions
        function testSearch() {
            const searchInput = parent.document.getElementById('search-input');
            const status = document.getElementById('search-status');
            
            if (searchInput) {
                searchInput.focus();
                searchInput.value = 'test search';
                searchInput.dispatchEvent(new Event('input'));
                status.className = 'status success';
                status.textContent = '✅ Search input found and tested successfully';
                console.log('Search test passed');
            } else {
                status.className = 'status error';
                status.textContent = '❌ Search input not found';
                console.error('Search test failed');
            }
        }
        
        function testSearchKeyboard() {
            const status = document.getElementById('search-status');
            const event = new KeyboardEvent('keydown', { ctrlKey: true, key: 'k' });
            parent.document.dispatchEvent(event);
            
            setTimeout(() => {
                const searchInput = parent.document.getElementById('search-input');
                if (searchInput === parent.document.activeElement) {
                    status.className = 'status success';
                    status.textContent = '✅ Ctrl+K shortcut working correctly';
                } else {
                    status.className = 'status error';
                    status.textContent = '❌ Ctrl+K shortcut not working';
                }
            }, 100);
        }
        
        function testBranchDropdown() {
            const branchSelector = parent.document.getElementById('branchSelector');
            const branchMenu = parent.document.getElementById('branchMenu');
            const status = document.getElementById('branch-status');
            
            if (branchSelector && branchMenu) {
                branchSelector.click();
                setTimeout(() => {
                    if (branchMenu.classList.contains('show')) {
                        status.className = 'status success';
                        status.textContent = '✅ Branch dropdown opens correctly';
                    } else {
                        status.className = 'status error';
                        status.textContent = '❌ Branch dropdown not opening';
                    }
                }, 100);
            } else {
                status.className = 'status error';
                status.textContent = '❌ Branch selector elements not found';
            }
        }
        
        function testBranchSelection() {
            const branchMenu = parent.document.getElementById('branchMenu');
            const status = document.getElementById('branch-status');
            
            if (branchMenu) {
                const branchItem = branchMenu.querySelector('.dropdown-item[data-branch="north"]');
                if (branchItem) {
                    branchItem.click();
                    setTimeout(() => {
                        const currentBranchName = parent.document.getElementById('currentBranchName');
                        if (currentBranchName && currentBranchName.textContent === 'North Branch') {
                            status.className = 'status success';
                            status.textContent = '✅ Branch selection working correctly';
                        } else {
                            status.className = 'status error';
                            status.textContent = '❌ Branch selection not updating';
                        }
                    }, 100);
                } else {
                    status.className = 'status error';
                    status.textContent = '❌ Branch item not found';
                }
            } else {
                status.className = 'status error';
                status.textContent = '❌ Branch menu not found';
            }
        }
        
        function testLanguageDropdown() {
            const languageSelector = parent.document.getElementById('languageSelector');
            const languageMenu = parent.document.getElementById('languageMenu');
            const status = document.getElementById('language-status');
            
            if (languageSelector && languageMenu) {
                languageSelector.click();
                setTimeout(() => {
                    if (languageMenu.classList.contains('show')) {
                        status.className = 'status success';
                        status.textContent = '✅ Language dropdown opens correctly';
                    } else {
                        status.className = 'status error';
                        status.textContent = '❌ Language dropdown not opening';
                    }
                }, 100);
            } else {
                status.className = 'status error';
                status.textContent = '❌ Language selector elements not found';
            }
        }
        
        function testLanguageSelection() {
            const languageMenu = parent.document.getElementById('languageMenu');
            const status = document.getElementById('language-status');
            
            if (languageMenu) {
                const languageItem = languageMenu.querySelector('.dropdown-item[data-language="fr"]');
                if (languageItem) {
                    languageItem.click();
                    setTimeout(() => {
                        const currentLanguageName = parent.document.getElementById('currentLanguageName');
                        if (currentLanguageName && currentLanguageName.textContent === 'Français') {
                            status.className = 'status success';
                            status.textContent = '✅ Language selection working correctly';
                        } else {
                            status.className = 'status error';
                            status.textContent = '❌ Language selection not updating';
                        }
                    }, 100);
                } else {
                    status.className = 'status error';
                    status.textContent = '❌ Language item not found';
                }
            } else {
                status.className = 'status error';
                status.textContent = '❌ Language menu not found';
            }
        }
        
        function testHeaderButtons() {
            const status = document.getElementById('buttons-status');
            const logoutButton = parent.document.getElementById('logoutButton');
            
            if (logoutButton) {
                status.className = 'status success';
                status.textContent = '✅ Header buttons found and accessible';
            } else {
                status.className = 'status error';
                status.textContent = '❌ Header buttons not found';
            }
        }
        
        function testMenuBarButtons() {
            const status = document.getElementById('buttons-status');
            const releaseNotesButton = parent.document.getElementById('releaseNotesButtonMenu');
            const customizeButton = parent.document.getElementById('customizeButtonMenu');
            const monthEndButton = parent.document.getElementById('monthEndButtonMenu');
            const userManualButton = parent.document.getElementById('userManualButtonMenu');
            
            const foundButtons = [releaseNotesButton, customizeButton, monthEndButton, userManualButton].filter(btn => btn);
            
            if (foundButtons.length === 4) {
                status.className = 'status success';
                status.textContent = `✅ All 4 menu bar buttons found and accessible`;
            } else {
                status.className = 'status error';
                status.textContent = `❌ Only ${foundButtons.length}/4 menu bar buttons found`;
            }
        }
        
        function testKeyboardShortcuts() {
            const status = document.getElementById('keyboard-status');
            status.className = 'status info';
            status.innerHTML = `
                <strong>Testing keyboard shortcuts...</strong><br>
                • Try Ctrl+K to focus search<br>
                • Try Alt+B to open branch selector<br>
                • Try Alt+L to open language selector<br>
                • Try Escape to close dropdowns<br>
                <em>Check console for detailed results</em>
            `;
        }
        
        // Auto-run basic tests on load
        window.addEventListener('load', function() {
            setTimeout(() => {
                console.log('Running automatic header functionality tests...');
                testSearch();
                setTimeout(testBranchDropdown, 500);
                setTimeout(testLanguageDropdown, 1000);
                setTimeout(testHeaderButtons, 1500);
                setTimeout(testMenuBarButtons, 2000);
            }, 1000);
        });
    </script>
</body>
</html>
